const MealPlan = require('../models/MealPlan');
const User = require('../models/User');
const Meal = require('../models/Meal');

// Helper function to track meals added from templates
const trackMealsFromTemplate = async (userId, date, mealPlan) => {
  try {
    const user = await User.findById(userId);
    if (!user) return;

    const mealTypes = ['breakfast', 'lunch', 'dinner', 'snack'];

    for (const mealType of mealTypes) {
      const meals = mealPlan[mealType] || [];

      for (const meal of meals) {
        // Remove if already exists (by name)
        user.recentlyAddedToMealPlans = user.recentlyAddedToMealPlans.filter(
          item => item.name !== meal.name
        );

        // Add to the beginning with additional metadata
        user.recentlyAddedToMealPlans.unshift({
          ...meal,
          addedAt: new Date(),
          addedToDate: date,
          addedToMealType: mealType
        });
      }
    }

    // Limit to last 15 meals
    if (user.recentlyAddedToMealPlans.length > 15) {
      user.recentlyAddedToMealPlans = user.recentlyAddedToMealPlans.slice(0, 15);
    }

    await user.save();
  } catch (error) {
    console.error('Error tracking meals from template:', error);
  }
};

// Helper function to track single meal addition
const trackSingleMealAddition = async (userId, date, mealType, meal) => {
  try {
    const user = await User.findById(userId);
    if (!user) return;

    // Remove if already exists (by name)
    user.recentlyAddedToMealPlans = user.recentlyAddedToMealPlans.filter(
      item => item.name !== meal.name
    );

    // Add to the beginning with additional metadata
    user.recentlyAddedToMealPlans.unshift({
      ...meal,
      addedAt: new Date(),
      addedToDate: date,
      addedToMealType: mealType
    });

    // Limit to last 15 meals
    if (user.recentlyAddedToMealPlans.length > 15) {
      user.recentlyAddedToMealPlans = user.recentlyAddedToMealPlans.slice(0, 15);
    }

    await user.save();
  } catch (error) {
    console.error('Error tracking single meal addition:', error);
  }
};

// Get all meal plans
exports.getMealPlans = async (req, res) => {
  try {
    const userId = req.user.id;
    const { startDate, endDate } = req.query;

    let query = { user: userId };

    if (startDate && endDate) {
      query.date = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    const mealPlans = await MealPlan.find(query)
      .populate('meals.meal')
      .sort({ date: 1 });

    res.json(mealPlans);
  } catch (error) {
    console.error('Error fetching meal plans:', error);
    res.status(500).json({
      message: 'Error fetching meal plans',
      error: error.message
    });
  }
};
exports.getSavedMealPlans = async (req, res) => {
  try {
    const userId = req.user.id;

    // Get unique meal plan names for this user
    const savedPlans = await MealPlan.aggregate([
      { $match: { user: userId, name: { $exists: true, $ne: null } } },
      {
        $group: {
          _id: '$name',
          startDate: { $min: '$date' },
          endDate: { $max: '$date' },
          dietaryPreference: { $first: '$dietaryPreference' },
          mealCount: { $sum: { $size: '$meals' } },
          createdAt: { $first: '$createdAt' }
        }
      },
      { $sort: { createdAt: -1 } }
    ]);

    res.json(savedPlans);
  } catch (error) {
    console.error('Error fetching saved meal plans:', error);
    res.status(500).json({
      message: 'Error fetching saved meal plans',
      error: error.message
    });
  }
};

// Get meal plan by ID
exports.getMealPlanById = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const mealPlan = await MealPlan.findOne({
      _id: id,
      user: userId
    }).populate('meals.meal');

    if (!mealPlan) {
      return res.status(404).json({ message: 'Meal plan not found' });
    }

    // Transform the meal plan data to include detailed meals from individual meal type arrays
    const transformedMealPlan = {
      ...mealPlan.toObject(),
      meals: []
    };

    // Add meals from breakfast, lunch, dinner, and snack arrays
    const mealTypes = ['breakfast', 'lunch', 'dinner', 'snack'];

    mealTypes.forEach(mealType => {
      if (mealPlan[mealType] && mealPlan[mealType].length > 0) {
        mealPlan[mealType].forEach(meal => {
          transformedMealPlan.meals.push({
            date: mealPlan.date,
            mealType: mealType,
            mealData: {
              name: meal.name,
              calories: meal.calories,
              protein: meal.protein,
              carbs: meal.carbs,
              fat: meal.fat,
              image: meal.image,
              description: meal.description,
              ingredients: meal.ingredients,
              instructions: meal.instructions,
              category: meal.category,
              dietaryTags: meal.dietaryTags
            }
          });
        });
      }
    });

    res.json(transformedMealPlan);
  } catch (error) {
    console.error('Error fetching meal plan by ID:', error);
    res.status(500).json({
      message: 'Error fetching meal plan',
      error: error.message
    });
  }
};

// Get meal plan by date
exports.getMealPlanByDate = async (req, res) => {
  try {
    const { date } = req.params;
    const userId = req.user.id;

    // Convert date to string format if it's not already
    const dateStr = new Date(date).toISOString().split('T')[0];

    const mealPlan = await MealPlan.findOne({
      user: userId,
      date: dateStr
    }).populate('meals.meal');

    if (!mealPlan) {
      return res.status(404).json({ message: 'Meal plan not found for this date' });
    }

    res.json(mealPlan);
  } catch (error) {
    console.error('Error fetching meal plan by date:', error);
    res.status(500).json({
      message: 'Error fetching meal plan',
      error: error.message
    });
  }
};

exports.saveMealPlan = async (req, res) => {
  try {
    const { name, startDate, endDate, dietaryPreference, meals, mealTimes, riceBowls, riceBowlsPerDay } = req.body;
    const userId = req.user?.id;

    // Check if user is authenticated
    if (!userId) {
      return res.status(401).json({
        message: 'User not authenticated',
        error: 'Missing user ID from authentication'
      });
    }

    console.log('Saving meal plan for user:', userId);

    if (!name || !startDate || !endDate || !meals || meals.length === 0) {
      return res.status(400).json({
        message: 'Name, start date, end date, and meals are required',
        received: { name, startDate, endDate, mealsCount: meals?.length }
      });
    }
    // Group meals by date and mealType
    const mealsByDate = {};
    meals.forEach(mealEntry => {
      const { date, mealType, mealData } = mealEntry;
      if (!mealsByDate[date]) {
        mealsByDate[date] = {
          breakfast: [],
          lunch: [],
          dinner: [],
          snack: [],
        };
      }
      if (['breakfast', 'lunch', 'dinner', 'snack'].includes(mealType)) {
        mealsByDate[date][mealType].push({
          ...mealData,
          // Convert category array to string if needed
          category: Array.isArray(mealData.category) ? mealData.category.join(', ') : mealData.category,
          instanceId: mealData.instanceId || `${mealData.name}-${Date.now()}`
        });
      }
    });
    // Save a MealPlan document for each date
    const savedPlans = [];
    for (const [date, mealsObj] of Object.entries(mealsByDate)) {
      await MealPlan.deleteMany({ user: userId, date }); // date as string
      const mealPlan = new MealPlan({
        user: userId,
        date,
        isTemplate: false,
        templateName: name,
        breakfast: mealsObj.breakfast,
        lunch: mealsObj.lunch,
        dinner: mealsObj.dinner,
        snack: mealsObj.snack,
        riceBowls: riceBowlsPerDay ? riceBowlsPerDay[date] || 0 : riceBowls || 0,
        mealTimes: mealTimes || {},
        isLocked: false,
        completedMeals: { breakfast: [], lunch: [], dinner: [], snack: [] }
      });
      console.log('About to save meal plan:', mealPlan);
      await mealPlan.save();
      savedPlans.push(mealPlan);

      // Track meals added to meal plans
      await trackMealsFromTemplate(userId, date, mealPlan);
    }
    res.status(201).json({
      success: true,
      message: 'Meal plan saved successfully',
      planName: name,
      datesCount: Object.keys(mealsByDate).length,
      totalMeals: meals.length,
      savedPlans
    });
  } catch (error) {
    console.error('Error saving meal plan:', error);
    res.status(500).json({ message: 'Error saving meal plan', error: error.message });
  }
};

// Create or update meal plan
exports.createOrUpdateMealPlan = async (req, res) => {
  try {
    const { date, mealType, meal, riceBowls } = req.body;
    const userId = req.user.id;

    console.log('Creating/updating meal plan for date:', date, 'meal type:', mealType, 'user:', userId);

    if (!date || !mealType || !meal) {
      return res.status(400).json({
        message: 'Date, meal type, and meal are required',
        received: { date, mealType, meal }
      });
    }

    // Find existing meal plan for this date and user
    let mealPlan = await MealPlan.findOne({
      user: userId,
      date: new Date(date)
    });

    if (mealPlan) {
      // Update existing meal plan
      console.log('Updating existing meal plan');

      // Initialize meals array if it doesn't exist
      if (!mealPlan.meals) {
        mealPlan.meals = [];
      }

      // Update rice bowls if provided
      if (riceBowls !== undefined) {
        mealPlan.riceBowls = riceBowls;
      }

      // Add new meal to the plan
      mealPlan.meals.push({
        mealType: mealType,
        meal: meal._id || meal,
        scheduledTime: req.body.scheduledTime || null
      });

      await mealPlan.save();
    } else {
      // Create new meal plan
      console.log('Creating new meal plan for date:', date);

      mealPlan = new MealPlan({
        user: userId,
        date: new Date(date),
        riceBowls: riceBowls || 0,
        meals: [{
          mealType: mealType,
          meal: meal._id || meal,
          scheduledTime: req.body.scheduledTime || null
        }]
      });

      await mealPlan.save();
    }

    // Track meal added to meal plan
    await trackSingleMealAddition(userId, date, mealType, meal);

    // Populate the meal details
    await mealPlan.populate('meals.meal');

    res.status(201).json({
      message: 'Meal plan updated successfully',
      mealPlan: mealPlan
    });

  } catch (error) {
    console.error('Error creating/updating meal plan:', error);
    res.status(500).json({
      message: 'Error creating/updating meal plan',
      error: error.message
    });
  }
};

// Toggle lock status
exports.toggleLockMealPlan = async (req, res) => {
  try {
    const { date } = req.params;
    const { isLocked } = req.body;
    const userId = req.user.id;

    const mealPlan = await MealPlan.findOne({
      user: userId,
      date: new Date(date)
    });

    if (!mealPlan) {
      return res.status(404).json({ message: 'Meal plan not found' });
    }

    mealPlan.isLocked = isLocked;
    await mealPlan.save();

    res.json({
      message: 'Meal plan lock status updated',
      isLocked: mealPlan.isLocked
    });
  } catch (error) {
    console.error('Error updating meal plan lock status:', error);
    res.status(500).json({
      message: 'Error updating meal plan lock status',
      error: error.message
    });
  }
};

// Mark meal as completed
exports.markMealCompleted = async (req, res) => {
  try {
    const { date } = req.params;
    const { mealType, mealInstanceId, isCompleted } = req.body;
    const userId = req.user.id;

    const mealPlan = await MealPlan.findOne({
      user: userId,
      date: new Date(date)
    });

    if (!mealPlan) {
      return res.status(404).json({ message: 'Meal plan not found' });
    }

    // Find and update the specific meal
    const meal = mealPlan.meals.find(meal =>
      meal.mealType === mealType && meal._id.toString() === mealInstanceId
    );

    if (!meal) {
      return res.status(404).json({ message: 'Meal not found in plan' });
    }

    meal.isCompleted = isCompleted;
    await mealPlan.save();

    res.json({
      message: 'Meal completion status updated',
      meal: meal
    });
  } catch (error) {
    console.error('Error updating meal completion status:', error);
    res.status(500).json({
      message: 'Error updating meal completion status',
      error: error.message
    });
  }
};

// Remove meal from plan
exports.removeMealFromPlan = async (req, res) => {
  try {
    const { date } = req.params;
    const { mealType, mealInstanceId } = req.body;
    const userId = req.user.id;

    const mealPlan = await MealPlan.findOne({
      user: userId,
      date: new Date(date)
    });

    if (!mealPlan) {
      return res.status(404).json({ message: 'Meal plan not found' });
    }

    // Remove the specific meal
    mealPlan.meals = mealPlan.meals.filter(meal =>
      !(meal.mealType === mealType && meal._id.toString() === mealInstanceId)
    );

    await mealPlan.save();

    res.json({
      message: 'Meal removed successfully',
      mealPlan: mealPlan
    });
  } catch (error) {
    console.error('Error removing meal from plan:', error);
    res.status(500).json({
      message: 'Error removing meal from plan',
      error: error.message
    });
  }
};

// Delete meal plan
exports.deleteMealPlan = async (req, res) => {
  try {
    const { date } = req.params;
    const userId = req.user.id;

    await MealPlan.deleteMany({
      user: userId,
      date: new Date(date)
    });

    res.json({ message: 'Meal plan deleted successfully' });
  } catch (error) {
    console.error('Error deleting meal plan:', error);
    res.status(500).json({
      message: 'Error deleting meal plan',
      error: error.message
    });
  }
};

// Create meal plan from template
exports.createFromTemplate = async (req, res) => {
  try {
    const userId = req.user.id;
    const { templateId, date } = req.body;

    console.log(`Creating meal plan from template ${templateId} for date ${date} for user ${userId}`);

    if (!templateId || !date) {
      console.log('Template ID and date are required');
      return res.status(400).json({
        success: false,
        message: 'Template ID and date are required'
      });
    }

    // Find the user
    const user = await User.findById(userId);

    if (!user) {
      console.log('User not found');
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Find the template in user's saved templates
    const templateInfo = user.savedMealPlans.find(
      template => template._id.toString() === templateId
    );

    if (!templateInfo) {
      console.log('Template not found in user saved templates');
      return res.status(404).json({
        success: false,
        message: 'Template not found'
      });
    }

    // Get the template meal plan
    const templatePlan = await MealPlan.findById(templateInfo.plan);

    if (!templatePlan) {
      console.log('Template meal plan not found');
      return res.status(404).json({
        success: false,
        message: 'Template meal plan not found'
      });
    }

    // Check if a meal plan already exists for this date
    const existingPlan = await MealPlan.findOne({ user: userId, date });

    if (existingPlan) {
      console.log(`A meal plan already exists for date ${date}`);
      return res.status(400).json({
        success: false,
        message: 'A meal plan already exists for this date'
      });
    }

    // Create new meal plan based on template
    const newMealPlan = new MealPlan({
      user: userId,
      date,
      breakfast: templatePlan.breakfast,
      lunch: templatePlan.lunch,
      dinner: templatePlan.dinner,
      snack: templatePlan.snack,
      mealTimes: templatePlan.mealTimes,
      isLocked: false,
      completedMeals: {}
    });

    console.log('Saving new meal plan from template');
    await newMealPlan.save();

    // Track meals added to meal plans from template
    await trackMealsFromTemplate(userId, date, newMealPlan);

    res.status(201).json({
      success: true,
      message: 'Meal plan created from template',
      mealPlan: newMealPlan
    });
  } catch (error) {
    console.error('Error creating meal plan from template:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create meal plan from template',
      error: error.message
    });
  }
};

// Create template from existing meal plan
exports.createTemplateFromMealPlan = async (req, res) => {
  try {
    const userId = req.user.id;
    const { mealPlanId, templateName } = req.body;

    console.log(`Creating template "${templateName}" from meal plan ${mealPlanId} for user ${userId}`);

    if (!mealPlanId || !templateName) {
      console.log('Meal plan ID and template name are required');
      return res.status(400).json({
        success: false,
        message: 'Meal plan ID and template name are required'
      });
    }

    // Find the meal plan
    const mealPlan = await MealPlan.findOne({
      _id: mealPlanId,
      user: userId
    });

    if (!mealPlan) {
      console.log('Meal plan not found');
      return res.status(404).json({
        success: false,
        message: 'Meal plan not found'
      });
    }

    // Create a new meal plan as a template
    const templatePlan = new MealPlan({
      user: userId,
      date: 'template', // Special marker for templates
      isTemplate: true,
      templateName,
      breakfast: mealPlan.breakfast,
      lunch: mealPlan.lunch,
      dinner: mealPlan.dinner,
      snack: mealPlan.snack,
      mealTimes: mealPlan.mealTimes,
      isLocked: false,
      completedMeals: {}
    });

    console.log('Saving template meal plan');
    await templatePlan.save();

    // Add to user's saved templates
    const user = await User.findById(userId);

    if (!user.savedMealPlans) {
      user.savedMealPlans = [];
    }

    user.savedMealPlans.push({
      name: templateName,
      plan: templatePlan._id,
      createdAt: new Date()
    });

    console.log('Adding template to user saved meal plans');
    await user.save();

    res.status(201).json({
      success: true,
      message: 'Template created from meal plan',
      template: templatePlan,
      savedMealPlans: user.savedMealPlans
    });
  } catch (error) {
    console.error('Error creating template from meal plan:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create template from meal plan',
      error: error.message
    });
  }
};

// Get meal recommendations based on dietary preferences
exports.getMealRecommendations = async (req, res) => {
  try {
    const userId = req.user.id;
    const { mealType } = req.query; // breakfast, lunch, dinner, snack

    console.log(`Getting ${mealType} recommendations for user ${userId}`);

    if (!mealType) {
      console.log('Meal type is required');
      return res.status(400).json({
        success: false,
        message: 'Meal type is required'
      });
    }

    // Get user with preferences
    const user = await User.findById(userId);

    if (!user) {
      console.log('User not found');
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get dietary preferences
    const preferences = user.dietaryPreferences || {};
    console.log('User preferences:', preferences);

    // Get all meals from your meal controller
    const Meal = require('../models/Meal');

    // Build query based on preferences
    const query = { category: mealType.toLowerCase() };

    // Handle dietary restrictions
    if (preferences.restrictions && preferences.restrictions.length > 0) {
      const dietaryTags = [];

      preferences.restrictions.forEach(restriction => {
        switch(restriction) {
          case 'Vegetarian':
            dietaryTags.push('vegetarian');
            break;
          case 'Vegan':
            dietaryTags.push('vegan');
            break;
          case 'Gluten-Free':
            dietaryTags.push('gluten-free');
            break;
          case 'Dairy-Free':
            dietaryTags.push('dairy-free');
            break;
          case 'Nut-Free':
            dietaryTags.push('nut-free');
            break;
          case 'Low-Carb':
            dietaryTags.push('low-carb');
            break;
          case 'Keto':
            dietaryTags.push('keto');
            break;
          case 'Paleo':
            dietaryTags.push('paleo');
            break;
        }
      });

      if (dietaryTags.length > 0) {
        query.tags = { $in: dietaryTags };
      }
    }

    // Handle allergies and disliked ingredients
    const excludedIngredients = [
      ...(preferences.allergies || []),
      ...(preferences.dislikedIngredients || [])
    ];

    if (excludedIngredients.length > 0) {
      query['ingredients.name'] = { $nin: excludedIngredients };
    }

    console.log('Query for meal recommendations:', query);

    // Get meals based on query
    let meals = await Meal.find(query).limit(10);

    // If no meals found with restrictions, get some default ones
    if (meals.length === 0) {
      console.log('No meals found with restrictions, getting default meals');
      meals = await Meal.find({ category: mealType.toLowerCase() }).limit(10);
    }

    console.log(`Found ${meals.length} meal recommendations`);

    res.status(200).json({
      success: true,
      recommendations: meals
    });
  } catch (error) {
    console.error('Error getting meal recommendations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get meal recommendations',
      error: error.message
    });
  }
};

// Update meal times
exports.updateMealTimes = async (req, res) => {
  try {
    const { date } = req.params;
    const { mealTimes } = req.body;
    const userId = req.user.id;

    const mealPlan = await MealPlan.findOne({
      user: userId,
      date: new Date(date)
    });

    if (!mealPlan) {
      return res.status(404).json({ message: 'Meal plan not found' });
    }

    mealPlan.mealTimes = mealTimes;
    await mealPlan.save();

    res.json({
      message: 'Meal times updated successfully',
      mealTimes: mealPlan.mealTimes
    });
  } catch (error) {
    console.error('Error updating meal times:', error);
    res.status(500).json({
      message: 'Error updating meal times',
      error: error.message
    });
  }
};

// Get user's meal plans with filtering options
exports.getUserMealPlans = async (req, res) => {
  try {
    const userId = req.user.id;
    const { startDate, endDate, includeTemplates } = req.query;

    console.log(`Getting meal plans for user ${userId} from ${startDate} to ${endDate}`);

    const query = { user: userId };

    // Add date range filter if provided
    if (startDate && endDate) {
      query.date = { $gte: startDate, $lte: endDate };
    }

    // Exclude templates by default
    if (includeTemplates !== 'true') {
      query.isTemplate = { $ne: true };
    }

    const mealPlans = await MealPlan.find(query).sort({ date: 1 });

    console.log(`Found ${mealPlans.length} meal plans`);

    res.status(200).json({
      success: true,
      mealPlans
    });
  } catch (error) {
    console.error('Error getting user meal plans:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get user meal plans',
      error: error.message
    });
  }
};

// Add placeholder functions for the existing routes
exports.createFromTemplate = async (req, res) => {
  res.status(501).json({ message: 'Function not implemented yet' });
};

exports.createTemplateFromMealPlan = async (req, res) => {
  res.status(501).json({ message: 'Function not implemented yet' });
};

exports.getMealRecommendations = async (req, res) => {
  res.status(501).json({ message: 'Function not implemented yet' });
};

exports.getUserMealPlans = async (req, res) => {
  res.status(501).json({ message: 'Function not implemented yet' });
};

// Generate meal plan automatically based on user preferences
exports.generateMealPlan = async (req, res) => {
  try {
    const { startDate, endDate, includeFamily, calorieTarget } = req.body;
    const userId = req.user.id;

    console.log(`Generating meal plan for user ${userId} from ${startDate} to ${endDate}`);

    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'Start date and end date are required'
      });
    }

    // Get user with preferences
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Calculate date range
    const start = new Date(startDate);
    const end = new Date(endDate);
    const dates = [];

    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      dates.push(new Date(d).toISOString().split('T')[0]);
    }

    if (dates.length > 14) {
      return res.status(400).json({
        success: false,
        message: 'Cannot generate meal plans for more than 14 days at once'
      });
    }

    // Collect all dietary preferences (user + family if requested)
    const allRestrictions = [...(user.dietaryPreferences?.restrictions || [])];
    const allAllergies = [...(user.dietaryPreferences?.allergies || [])];
    const allDislikedIngredients = [...(user.dietaryPreferences?.dislikedIngredients || [])];

    if (includeFamily && user.familyMembers && user.familyMembers.length > 0) {
      user.familyMembers.forEach(member => {
        if (member.dietaryPreferences) {
          allRestrictions.push(...(member.dietaryPreferences.restrictions || []));
          allAllergies.push(...(member.dietaryPreferences.allergies || []));
          allDislikedIngredients.push(...(member.dietaryPreferences.dislikedIngredients || []));
        }
      });
    }

    // Remove duplicates
    const uniqueRestrictions = [...new Set(allRestrictions)];
    const uniqueAllergies = [...new Set(allAllergies)];
    const uniqueDislikedIngredients = [...new Set(allDislikedIngredients)];

    // Calculate calorie targets
    const targetCalories = calorieTarget || user.dietaryPreferences?.calorieTarget || 2000;
    const mealFrequency = user.dietaryPreferences?.mealFrequency || 3;
    const caloriesPerMeal = targetCalories / mealFrequency;

    console.log(`Target calories: ${targetCalories}, meals per day: ${mealFrequency}, calories per meal: ${caloriesPerMeal}`);

    // Generate meal plan for each date
    const generatedPlans = [];
    const mealTypes = ['breakfast', 'lunch', 'dinner'];

    for (const date of dates) {
      console.log(`Generating meals for ${date}`);

      const dayMeals = {};

      for (const mealType of mealTypes) {
        const meals = await generateMealsForType(
          mealType,
          uniqueRestrictions,
          uniqueAllergies,
          uniqueDislikedIngredients,
          caloriesPerMeal,
          user
        );

        if (meals.length > 0) {
          // Select one meal randomly from the filtered options
          const selectedMeal = meals[Math.floor(Math.random() * meals.length)];
          dayMeals[mealType] = [selectedMeal];
        } else {
          dayMeals[mealType] = [];
        }
      }

      // Save the meal plan for this date
      await MealPlan.deleteMany({ user: userId, date });

      const mealPlan = new MealPlan({
        user: userId,
        date,
        isTemplate: false,
        templateName: `Generated Plan - ${date}`,
        breakfast: dayMeals.breakfast || [],
        lunch: dayMeals.lunch || [],
        dinner: dayMeals.dinner || [],
        snack: [],
        mealTimes: {
          breakfast: '08:00',
          lunch: '12:00',
          dinner: '18:00'
        },
        isLocked: false,
        completedMeals: { breakfast: [], lunch: [], dinner: [], snack: [] }
      });

      await mealPlan.save();
      generatedPlans.push(mealPlan);

      // Track meals added to meal plans
      await trackMealsFromTemplate(userId, date, mealPlan);
    }

    console.log(`Generated ${generatedPlans.length} meal plans`);

    res.status(201).json({
      success: true,
      message: 'Meal plans generated successfully',
      generatedPlans: generatedPlans.length,
      dateRange: { startDate, endDate },
      appliedFilters: {
        restrictions: uniqueRestrictions,
        allergies: uniqueAllergies,
        dislikedIngredients: uniqueDislikedIngredients,
        calorieTarget: targetCalories,
        includeFamily
      },
      plans: generatedPlans
    });

  } catch (error) {
    console.error('Error generating meal plan:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate meal plan',
      error: error.message
    });
  }
};

// Update meal plans for dietary changes
exports.updateMealPlansForDietaryChanges = async (req, res) => {
  try {
    const userId = req.user.id;
    const { dietaryPreferences } = req.body;

    console.log(`Manually updating meal plans for dietary changes for user ${userId}`);

    if (!dietaryPreferences) {
      return res.status(400).json({
        success: false,
        message: 'Dietary preferences are required'
      });
    }

    // Import the meal plan update service
    const { updateFutureMealPlansForDietaryChanges } = require('../services/mealPlanUpdateService');

    const updateResult = await updateFutureMealPlansForDietaryChanges(userId, dietaryPreferences);

    res.json({
      success: true,
      message: 'Meal plans updated successfully',
      result: updateResult
    });

  } catch (error) {
    console.error('Error updating meal plans for dietary changes:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update meal plans',
      error: error.message
    });
  }
};

// Check for dietary conflicts in future meal plans
exports.checkDietaryConflicts = async (req, res) => {
  try {
    const userId = req.user.id;
    const { dietaryPreferences } = req.query;

    console.log(`Checking dietary conflicts for user ${userId}`);

    // Get user's current dietary preferences if not provided
    let preferencesToCheck = dietaryPreferences;
    if (!preferencesToCheck) {
      const User = require('../models/User');
      const user = await User.findById(userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }
      preferencesToCheck = user.dietaryPreferences;
    } else {
      preferencesToCheck = JSON.parse(dietaryPreferences);
    }

    // Get today's date in YYYY-MM-DD format
    const today = new Date().toISOString().split('T')[0];

    // Find all future meal plans
    const futureMealPlans = await MealPlan.find({
      user: userId,
      date: { $gte: today },
      isTemplate: false
    }).sort({ date: 1 });

    const conflicts = [];
    const { checkMealConflict } = require('../services/mealPlanUpdateService');

    // Check each meal plan for conflicts
    for (const mealPlan of futureMealPlans) {
      const mealTypes = ['breakfast', 'lunch', 'dinner', 'snack'];

      for (const mealType of mealTypes) {
        if (mealPlan[mealType] && mealPlan[mealType].length > 0) {
          for (const meal of mealPlan[mealType]) {
            const conflictResult = checkMealConflict(meal, preferencesToCheck);

            if (conflictResult.hasConflict) {
              conflicts.push({
                date: mealPlan.date,
                mealType,
                mealName: meal.name,
                reasons: conflictResult.reasons,
                mealId: meal.instanceId || meal._id
              });
            }
          }
        }
      }
    }

    res.json({
      success: true,
      conflicts,
      totalConflicts: conflicts.length,
      plansChecked: futureMealPlans.length
    });

  } catch (error) {
    console.error('Error checking dietary conflicts:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to check dietary conflicts',
      error: error.message
    });
  }
};

// Resolve dietary conflicts by replacing conflicting meals
exports.resolveDietaryConflicts = async (req, res) => {
  try {
    const userId = req.user.id;
    const { conflicts, dietaryPreferences } = req.body;

    console.log(`Resolving ${conflicts?.length || 0} dietary conflicts for user ${userId}`);

    if (!conflicts || !Array.isArray(conflicts) || conflicts.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Conflicts array is required'
      });
    }

    if (!dietaryPreferences) {
      return res.status(400).json({
        success: false,
        message: 'Dietary preferences are required'
      });
    }

    const { getAIReplacementMeal } = require('../services/mealPlanUpdateService');
    const resolutions = [];
    let resolvedCount = 0;

    // Group conflicts by date and meal plan
    const conflictsByPlan = {};
    for (const conflict of conflicts) {
      if (!conflictsByPlan[conflict.date]) {
        conflictsByPlan[conflict.date] = [];
      }
      conflictsByPlan[conflict.date].push(conflict);
    }

    // Resolve conflicts for each meal plan
    for (const [date, planConflicts] of Object.entries(conflictsByPlan)) {
      const mealPlan = await MealPlan.findOne({
        user: userId,
        date: date
      });

      if (!mealPlan) {
        console.log(`Meal plan not found for date ${date}`);
        continue;
      }

      let planUpdated = false;

      for (const conflict of planConflicts) {
        const meals = mealPlan[conflict.mealType];
        if (!meals) continue;

        // Find the conflicting meal
        const mealIndex = meals.findIndex(meal =>
          meal.name === conflict.mealName ||
          meal.instanceId === conflict.mealId ||
          meal._id?.toString() === conflict.mealId
        );

        if (mealIndex === -1) {
          console.log(`Meal not found: ${conflict.mealName}`);
          continue;
        }

        const originalMeal = meals[mealIndex];

        // Get AI replacement
        const replacementMeal = await getAIReplacementMeal(
          originalMeal,
          conflict.mealType,
          dietaryPreferences
        );

        if (replacementMeal) {
          // Replace the meal
          meals[mealIndex] = replacementMeal;
          planUpdated = true;
          resolvedCount++;

          resolutions.push({
            date,
            mealType: conflict.mealType,
            originalMeal: originalMeal.name,
            replacementMeal: replacementMeal.name,
            reason: conflict.reasons.join(', '),
            aiReason: replacementMeal.aiReason || 'AI-selected replacement'
          });

          console.log(`Resolved conflict: replaced "${originalMeal.name}" with "${replacementMeal.name}" on ${date}`);
        } else {
          console.log(`Could not find replacement for "${originalMeal.name}" on ${date}`);
        }
      }

      if (planUpdated) {
        mealPlan.updatedAt = new Date();
        await mealPlan.save();
      }
    }

    res.json({
      success: true,
      message: `Resolved ${resolvedCount} out of ${conflicts.length} conflicts`,
      resolvedCount,
      totalConflicts: conflicts.length,
      resolutions
    });

  } catch (error) {
    console.error('Error resolving dietary conflicts:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to resolve dietary conflicts',
      error: error.message
    });
  }
};

// Helper function to generate meals for a specific meal type
const generateMealsForType = async (mealType, restrictions, allergies, dislikedIngredients, calorieTarget, user) => {
  try {
    // Build query for meal type
    let query = { category: { $in: [mealType.toLowerCase()] } };

    // Apply dietary restrictions
    if (restrictions.length > 0) {
      const dietaryQuery = {};

      restrictions.forEach(restriction => {
        switch(restriction) {
          case 'Vegetarian':
            dietaryQuery['dietType.isVegetarian'] = true;
            break;
          case 'Vegan':
            dietaryQuery['dietType.isVegan'] = true;
            break;
          case 'Gluten-Free':
            dietaryQuery['dietType.isGlutenFree'] = true;
            break;
          case 'Dairy-Free':
            dietaryQuery['dietType.isDairyFree'] = true;
            break;
          case 'Nut-Free':
            dietaryQuery['dietType.isNutFree'] = true;
            break;
          case 'Low-Carb':
            dietaryQuery['dietType.isLowCarb'] = true;
            break;
          case 'Keto':
            dietaryQuery['dietType.isKeto'] = true;
            break;
          case 'Pescatarian':
            dietaryQuery['dietType.isPescatarian'] = true;
            break;
          case 'Halal':
            dietaryQuery['dietType.isHalal'] = true;
            break;
        }
      });

      Object.assign(query, dietaryQuery);
    }

    // Exclude allergies and disliked ingredients
    const excludedIngredients = [...allergies, ...dislikedIngredients];
    if (excludedIngredients.length > 0) {
      query.ingredients = { $nin: excludedIngredients };
    }

    // Apply calorie filtering (with tolerance)
    const tolerance = calorieTarget * 0.4; // 40% tolerance
    query.calories = {
      $gte: Math.max(calorieTarget - tolerance, 0),
      $lte: calorieTarget + tolerance
    };

    console.log(`Query for ${mealType}:`, JSON.stringify(query, null, 2));

    // Get meals
    let meals = await Meal.find(query).limit(50);

    // If no meals found with calorie restriction, try without it
    if (meals.length === 0) {
      delete query.calories;
      meals = await Meal.find(query).limit(50);
      console.log(`Found ${meals.length} meals for ${mealType} without calorie restriction`);
    } else {
      console.log(`Found ${meals.length} meals for ${mealType} with calorie restriction`);
    }

    // Filter out recently used meals (last 7 days)
    if (user.recentlyAddedToMealPlans && user.recentlyAddedToMealPlans.length > 0) {
      const recentMealNames = user.recentlyAddedToMealPlans
        .filter(recent => {
          const daysSince = (Date.now() - new Date(recent.addedAt).getTime()) / (1000 * 60 * 60 * 24);
          return daysSince < 7;
        })
        .map(recent => recent.name);

      meals = meals.filter(meal => !recentMealNames.includes(meal.name));
      console.log(`After filtering recent meals: ${meals.length} meals remaining`);
    }

    // Convert to plain objects and add instance IDs
    return meals.map(meal => ({
      name: meal.name,
      mealType: [mealType],
      category: Array.isArray(meal.category) ? meal.category.join(', ') : meal.category,
      dietaryTags: meal.dietaryTags || [],
      calories: meal.calories,
      protein: meal.protein,
      carbs: meal.carbs,
      fat: meal.fat,
      image: meal.image,
      description: meal.description,
      ingredients: meal.ingredients || [],
      instructions: meal.instructions || [],
      instanceId: `${meal.name}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    }));

  } catch (error) {
    console.error(`Error generating meals for ${mealType}:`, error);
    return [];
  }
};


