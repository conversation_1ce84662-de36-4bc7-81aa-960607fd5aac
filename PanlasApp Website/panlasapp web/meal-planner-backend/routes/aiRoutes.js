const express = require('express');
const router = express.Router();
const geminiService = require('../services/geminiService');
const Meal = require('../models/Meal');
const User = require('../models/User');
const auth = require('../middleware/auth');

// Detect dietary conflicts
router.post('/dietary-conflicts', auth, async (req, res) => {
  try {
    // Handle both old format (direct preferences) and new format (with user and family)
    let requestData;

    if (req.body.userPreferences) {
      // New format with user and family preferences
      requestData = {
        userPreferences: req.body.userPreferences,
        familyMembers: req.body.familyMembers || []
      };
    } else {
      // Old format - direct preferences (for backward compatibility)
      const { restrictions, allergies, dislikedIngredients } = req.body;
      requestData = {
        restrictions,
        allergies,
        dislikedIngredients
      };
    }

    console.log('Dietary conflicts request data:', requestData);

    const conflicts = await geminiService.detectDietaryConflicts(requestData);

    res.json({
      success: true,
      conflicts
    });
  } catch (error) {
    console.error('Error detecting dietary conflicts:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to detect dietary conflicts',
      error: error.message
    });
  }
});

// Generate personalized meal recommendations
router.post('/meal-recommendations', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const { goalType, limit = 10 } = req.body;

    // Get user profile with family members
    const user = await User.findById(userId).select('dietaryPreferences familyMembers');
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get all available meals from database
    const meals = await Meal.find(); // Get all meals for better recommendations

    // Generate AI recommendations
    const recommendations = await geminiService.generateMealRecommendations(
      {
        dietaryPreferences: user.dietaryPreferences,
        familyMembers: user.familyMembers
      },
      meals,
      goalType
    );

    // Map AI recommendations to actual meal objects from database
    const recommendedMeals = [];
    for (const rec of recommendations.recommendations) {
      const meal = meals.find(m =>
        m.name.toLowerCase() === rec.mealName.toLowerCase()
      );
      if (meal && recommendedMeals.length < limit) {
        recommendedMeals.push({
          ...meal.toObject(),
          aiReason: rec.reason,
          nutritionalBenefits: rec.nutritionalBenefits,
          suitability: rec.suitability
        });
      }
    }

    res.json({
      success: true,
      recommendations: recommendedMeals,
      generalAdvice: recommendations.generalAdvice,
      totalFound: recommendedMeals.length
    });
  } catch (error) {
    console.error('Error generating meal recommendations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate meal recommendations',
      error: error.message
    });
  }
});

// Generate goal-based dietary suggestions
router.post('/goal-suggestions', auth, async (req, res) => {
  try {
    const { goal, healthCondition } = req.body;

    if (!goal) {
      return res.status(400).json({
        success: false,
        message: 'Goal is required'
      });
    }

    const suggestions = await geminiService.generateGoalBasedSuggestions(goal, healthCondition);

    res.json({
      success: true,
      suggestions
    });
  } catch (error) {
    console.error('Error generating goal suggestions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate goal suggestions',
      error: error.message
    });
  }
});

// Generate AI meal plan
router.post('/generate-meal-plan', auth, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get user profile with family members
    const user = await User.findById(userId).select('dietaryPreferences familyMembers');
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get all available meals from database
    const meals = await Meal.find();

    // Generate AI meal plan
    const mealPlanResult = await geminiService.generateAIMealPlan(
      {
        dietaryPreferences: user.dietaryPreferences,
        familyMembers: user.familyMembers
      },
      meals
    );

    res.json({
      success: true,
      mealPlan: mealPlanResult.mealPlan,
      nutritionalSummary: mealPlanResult.nutritionalSummary,
      personalizedMessage: mealPlanResult.personalizedMessage,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error generating AI meal plan:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate AI meal plan',
      error: error.message
    });
  }
});

// Generate family meal plan
router.post('/generate-family-meal-plan', auth, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get user profile with family members
    const user = await User.findById(userId).select('dietaryPreferences familyMembers');
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check if user has family members
    if (!user.familyMembers || user.familyMembers.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No family members found. Please add family members first to generate a family meal plan.'
      });
    }

    // Get all available meals from database
    const meals = await Meal.find();

    // Generate family meal plan with conflict detection
    const mealPlanResult = await geminiService.generateFamilyMealPlan(
      {
        dietaryPreferences: user.dietaryPreferences,
        familyMembers: user.familyMembers
      },
      meals
    );

    res.json({
      success: true,
      mealPlan: mealPlanResult.mealPlan,
      nutritionalSummary: mealPlanResult.nutritionalSummary,
      personalizedMessage: mealPlanResult.personalizedMessage,
      conflicts: mealPlanResult.conflicts || [],
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error generating family meal plan:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate family meal plan',
      error: error.message
    });
  }
});

// Edit meal plan
router.post('/edit-meal-plan', auth, async (req, res) => {
  try {
    const { currentMealPlan, editRequest, isFamily = false } = req.body;

    if (!currentMealPlan || !editRequest) {
      return res.status(400).json({
        success: false,
        message: 'Current meal plan and edit request are required'
      });
    }

    // Get user profile with family members if needed
    const user = await User.findById(req.user.id).select('dietaryPreferences familyMembers');
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const userProfile = {
      dietaryPreferences: user.dietaryPreferences || {
        restrictions: [],
        allergies: [],
        dislikedIngredients: [],
        calorieTarget: 2000,
        mealFrequency: 3
      },
      familyMembers: isFamily ? (user.familyMembers || []) : []
    };

    // Get all available meals from database
    const meals = await Meal.find();

    // Generate edited meal plan
    const editedMealPlan = await geminiService.editMealPlan(
      userProfile,
      meals,
      currentMealPlan,
      editRequest
    );

    res.json({
      success: true,
      mealPlan: editedMealPlan.mealPlan,
      nutritionalSummary: editedMealPlan.nutritionalSummary,
      personalizedMessage: editedMealPlan.personalizedMessage,
      conflicts: editedMealPlan.conflicts || [],
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error editing meal plan:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to edit meal plan',
      error: error.message
    });
  }
});

// Chat endpoint for general AI assistance
router.post('/chat', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const { message, includeProfile = false, includeMeals = true } = req.body;

    if (!message) {
      return res.status(400).json({
        success: false,
        message: 'Message is required'
      });
    }

    let context = {};

    if (includeProfile) {
      const user = await User.findById(userId).select('dietaryPreferences familyMembers');
      if (user) {
        context.familyProfile = {
          dietaryPreferences: user.dietaryPreferences,
          familyMembers: user.familyMembers
        };
      }
    }

    // Include available meals for better context
    if (includeMeals) {
      const meals = await Meal.find().select('name category mealType dietaryTags').limit(50);
      context.availableMeals = meals;
    }

    const response = await geminiService.generateChatResponse(message, context);

    res.json({
      success: true,
      response,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error generating chat response:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate chat response',
      error: error.message
    });
  }
});

// Get available goals and health conditions
router.get('/goals', (req, res) => {
  res.json({
    success: true,
    goals: [
      {
        id: 'lose_weight',
        name: 'Lose Weight',
        description: 'Get meal suggestions to help with weight loss'
      },
      {
        id: 'build_muscle',
        name: 'Build Muscle',
        description: 'Get high-protein meals to support muscle building'
      },
      {
        id: 'manage_health',
        name: 'Manage a Health Condition',
        description: 'Get meals tailored to specific health conditions'
      },
      {
        id: 'eat_sustainably',
        name: 'Eat Sustainably',
        description: 'Get environmentally conscious meal suggestions'
      },
      {
        id: 'generate_meal_plan',
        name: 'Generate a meal plan',
        description: 'Create a personalized daily meal plan based on your dietary preferences'
      },
      {
        id: 'generate_family_meal_plan',
        name: 'Generate a meal plan for family',
        description: 'Create a meal plan considering your family members\' dietary preferences and allergies'
      }
    ],
    healthConditions: [
      {
        id: 'type2_diabetes',
        name: 'Type 2 Diabetes',
        description: 'Low-sugar, low-carb meal recommendations'
      },
      {
        id: 'celiac_disease',
        name: 'Celiac Disease',
        description: 'Gluten-free meal recommendations'
      },
      {
        id: 'hypertension',
        name: 'Hypertension',
        description: 'Low-sodium meal recommendations'
      },
      {
        id: 'heart_disease',
        name: 'Heart Disease',
        description: 'Heart-healthy, low-cholesterol meals'
      },
      {
        id: 'lactose_intolerance',
        name: 'Lactose Intolerance',
        description: 'Dairy-free meal recommendations'
      }
    ]
  });
});

// Test Gemini API connection
router.get('/test', async (req, res) => {
  try {
    const testResponse = await geminiService.generateContent('Say hello and confirm you are working properly.');
    
    res.json({
      success: true,
      message: 'Gemini AI service is working',
      testResponse
    });
  } catch (error) {
    console.error('Gemini API test failed:', error);
    res.status(500).json({
      success: false,
      message: 'Gemini AI service test failed',
      error: error.message
    });
  }
});

module.exports = router;
