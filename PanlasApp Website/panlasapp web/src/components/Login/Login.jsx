import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import TermsModal from '../TermsModal/TermsModal';
import termsService from '../../services/termsService';
import analyticsService from '../../services/analyticsService';
import '../../../src/styles/Auth.css';

function Login() {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [apiError, setApiError] = useState('');
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [loginResponse, setLoginResponse] = useState(null);
  const navigate = useNavigate();

  // Check if user is already logged in
  // useEffect(() => {
  //   const token = localStorage.getItem('token');
  //   if (token) {
  //     navigate('/home');
  //   }
  // }, [navigate]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Clear API error when user makes changes
    if (apiError) {
      setApiError('');
    }

    // Real-time validation
    validateFieldRealTime(name, value);
  };

  const validateFieldRealTime = (fieldName, value) => {
    const newErrors = { ...errors };

    switch (fieldName) {
      case 'email':
        if (!value) {
          newErrors.email = 'Email is required';
        } else if (!/^[^\s@]+@gmail\.com$/i.test(value)) {
          newErrors.email = 'Email must be gmail';
        } else {
          delete newErrors.email;
        }
        break;

      case 'password':
        if (!value) {
          newErrors.password = 'Password is required';
        } else if (value.length < 5) {
          newErrors.password = 'Password must be at least 5 characters';
        } else {
          delete newErrors.password;
        }
        break;

      default:
        break;
    }

    setErrors(newErrors);
  };

  const validateForm = () => {
    const newErrors = {};
    
    // Email validation - Gmail only
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@gmail\.com$/i.test(formData.email)) {
      newErrors.email = 'Email must be gmail';
    }
    
    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 5) {
      newErrors.password = 'Password must be at least 5 characters';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    setApiError('');
    
    try {
      const response = await axios.post('http://localhost:5000/api/users/login', {
        email: formData.email,
        password: formData.password
      });
      
      // Store the token immediately after successful login
      console.log('🔍 Login response:', response.data);
      console.log('🔍 Requires terms acceptance:', response.data.requiresTermsAcceptance);

      // Always store the token first
      localStorage.setItem('token', response.data.token);
      console.log('🔑 Token stored in localStorage');

      if (response.data.requiresTermsAcceptance) {
        console.log('📋 Showing terms modal');
        // Store login response and show terms modal
        setLoginResponse(response.data);
        setShowTermsModal(true);
      } else {
        console.log('✅ No terms required, completing login');
        // Complete login process (this will also store user data)
        completeLogin(response.data);
      }
    } catch (err) {
      setApiError(
        err.response?.data?.message || 
        'Login failed. Please check your credentials and try again.'
      );
      console.error('Login error:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const completeLogin = (loginData) => {
    // Token is already stored, just store user info
    console.log('🏁 Completing login process');

    // Store user info (don't store sensitive data)
    if (loginData.user) {
      localStorage.setItem('user', JSON.stringify({
        id: loginData.user.id,
        email: loginData.user.email,
        username: loginData.user.username
      }));
      console.log('👤 User data stored in localStorage');
    }

    // Enable analytics tracking after successful login
    analyticsService.enable();

    // Redirect to home page after successful login
    console.log('🏠 Redirecting to home');
    navigate("/home");
  };

  const handleTermsAccept = async () => {
    console.log('🔄 Terms accept button clicked');
    try {
      console.log('🔄 Calling termsService.acceptTerms()');
      const result = await termsService.acceptTerms();
      console.log('📝 Terms acceptance result:', result);

      if (result.success) {
        console.log('✅ Terms accepted successfully, completing login');
        setShowTermsModal(false);
        // Complete the login process
        completeLogin(loginResponse);
      } else {
        console.error('❌ Terms acceptance failed:', result.error);
        setApiError('Failed to accept terms: ' + result.error);
      }
    } catch (error) {
      console.error('💥 Error accepting terms:', error);
      setApiError('Failed to accept terms. Please try again.');
    }
  };

  const handleTermsDecline = () => {
    console.log('❌ User declined terms');
    // Clear stored data and show error
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    setShowTermsModal(false);
    setLoginResponse(null);
    setApiError('You must accept the terms and conditions to use PanlasApp.');
  };

  return (
    <div className="auth-container">
      <div className="auth-box">
        <div className="heading">Welcome Back</div>
        
        {apiError && <div className="error-message">{apiError}</div>}
        
        <form className="form" onSubmit={handleSubmit}>
          <div className="input-group">
            <label htmlFor="email">Email Address</label>
            <input
              placeholder="Enter your email address"
              id="email"
              name="email"
              type="email"
              className={`input ${errors.email ? 'error' : ''}`}
              value={formData.email}
              onChange={handleChange}
            />
            {errors.email && <div className="validation-message">{errors.email}</div>}
          </div>

          <div className="input-group">
            <label htmlFor="password">Password</label>
            <input
              placeholder="Enter your password"
              id="password"
              name="password"
              type="password"
              className={`input ${errors.password ? 'error' : ''}`}
              value={formData.password}
              onChange={handleChange}
            />
            {errors.password && <div className="validation-message">{errors.password}</div>}
          </div>
          
          <div className="forgot-password">
            <Link to="/forgot-password">Forgot Password?</Link>
          </div>
          
          <button 
            type="submit" 
            className="auth-button"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Signing In...' : 'Sign In'}
          </button>
        </form>
        
        <div className="auth-link">
          <span>Don't have an account? </span>
          <Link to="/signup">Sign Up</Link>
        </div>
      </div>

      {/* Terms and Conditions Modal */}
      <TermsModal
        isOpen={showTermsModal}
        onAccept={handleTermsAccept}
        onDecline={handleTermsDecline}
      />
    </div>
  );
}

export default Login;
